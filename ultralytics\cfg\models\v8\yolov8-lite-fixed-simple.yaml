# 简化版YOLO知识蒸馏配置
# 解决维度不匹配问题的临时方案

# Parameters
nc: 80  # number of classes
scales: # model compound scaling constants
  # [depth, width, max_channels]
  n: [0.33, 0.25, 1024]  # YOLOv8n
  s: [0.33, 0.50, 1024]  # YOLOv8s

# 标准YOLOv8 backbone
backbone:
  # [from, repeats, module, args]
  - [-1, 1, Conv, [64, 3, 2]]  # 0-P1/2
  - [-1, 1, Conv, [128, 3, 2]]  # 1-P2/4
  - [-1, 3, C2f, [128, True]]   # 2
  - [-1, 1, Conv, [256, 3, 2]]  # 3-P3/8
  - [-1, 6, C2f, [256, True]]   # 4
  - [-1, 1, Conv, [512, 3, 2]]  # 5-P4/16
  - [-1, 6, C2f, [512, True]]   # 6
  - [-1, 1, Conv, [1024, 3, 2]] # 7-P5/32
  - [-1, 3, C2f, [1024, True]]  # 8
  - [-1, 1, <PERSON><PERSON><PERSON>, [1024, 5]]    # 9

# 标准YOLOv8 head
head:
  - [-1, 1, nn.<PERSON><PERSON><PERSON>, [None, 2, 'nearest']] # 10
  - [[-1, 6], 1, Concat, [1]]  # 11 cat backbone P4
  - [-1, 3, C2f, [512]]        # 12
  - [-1, 1, nn.Upsample, [None, 2, 'nearest']] # 13
  - [[-1, 4], 1, Concat, [1]]  # 14 cat backbone P3
  - [-1, 3, C2f, [256]]        # 15 (P3/8-small)
  - [-1, 1, Conv, [256, 3, 2]] # 16
  - [[-1, 12], 1, Concat, [1]] # 17 cat head P4
  - [-1, 3, C2f, [512]]        # 18 (P4/16-medium)
  - [-1, 1, Conv, [512, 3, 2]] # 19
  - [[-1, 9], 1, Concat, [1]]  # 20 cat head P5
  - [-1, 3, C2f, [1024]]       # 21 (P5/32-large)

  # 标准检测头 - 避免维度不匹配
  - [[15, 18, 21], 1, Detect, [nc]]  # 22 标准检测头
